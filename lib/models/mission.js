const mongoose = require('mongoose');
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')
const UserModel = require('./user');
const savedNotification = require('./savedNotification');
const generate = require('nanoid/generate')

const MissionSchema = new mongoose.Schema({
  code: {
    type: String,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  status: {
    type: Number,
    default: 1 //1: INITIAL, 2: IN_PROGRESS, 3: COMPLETED, 4: CANCELLED
  },
  location: {
    address: String,
    coordinates: {
      type: [Number],
      index: '2dsphere'
    }, 
  },
  type: {
    type: String,
    enum: ['self_assign', 'unit_assign'],
  },
  assignInfo: [{
    unit: { type: mongoose.Schema.Types.ObjectId, ref: 'Unit' },
    numberOfUsers: Number,
    users: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }]
  }],
  users: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User', default: [] }],
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Number, default: Date.now },
  updatedAt: { type: Number, default: Date.now },
  savedNotification: { type: mongoose.Schema.Types.ObjectId, ref: 'SavedNotification' }
}, { id: false, versionKey: false });

MissionSchema.pre('save', function (next) {
  let model = this
  // Nếu đã có code được truyền vào thì không cần generate mới
  if (model.code) {
    next()
  } else {
    attempToGenerate(model, next)
  }
})

const attempToGenerate = (model, callback) => {
  let newCode = generate('0123456789', 6)
  model.constructor.findOne({
    'code': newCode
  }).then((course) => {
    if (course) {
      attempToGenerate(model, callback)
    } else {
      model.code = newCode
      callback();
    }
  }, (err) => {
    callback(err)
  })
}


module.exports = mongoConnections('master').model('Mission', MissionSchema);
