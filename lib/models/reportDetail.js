const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

const ReportDetailSchema = new mongoose.Schema(
  {
    // Liên kết với Report (bắt buộc)
    reportId: {
      type: Schema.Types.ObjectId,
      ref: 'Report',
      required: true,
      index: true
    },

    // Thông tin thời gian
    time: {
      type: Number, // milliseconds timestamp
      index: true
    },

    // Thông tin địa điểm
    location: {
      address: String,
      coordinates: {
        type: [Number], // GeoJSON format: [lng, lat]
        index: '2dsphere'
      },
      // Giữ lại để tương thích
      lat: Number,
      lng: Number
    },

    // Khu vực (denormalized để query nhanh)
    areas: [{
      type: Schema.Types.ObjectId,
      ref: 'Area',
      index: true
    }],

    // Liên kết với JobType (chính là loại báo cáo)
    jobType: {
      type: Schema.Types.ObjectId,
      ref: 'JobType',
      required: true
    },

    // Timestamps
    createdAt: {
      type: Number,
      default: Date.now,
      index: true
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false }
)

// Indexes cho performance
ReportDetailSchema.index({ reportId: 1, time: 1 })
ReportDetailSchema.index({ reportId: 1, areas: 1 })
ReportDetailSchema.index({ time: 1, areas: 1 })
ReportDetailSchema.index({ areas: 1, createdAt: -1 })
ReportDetailSchema.index({ 'location.coordinates': '2dsphere' })
ReportDetailSchema.index({ jobType: 1, createdAt: 1 })

// Middleware để update updatedAt
ReportDetailSchema.pre('save', function(next) {
  this.updatedAt = Date.now()
  next()
})

module.exports = mongoConnections("master").model("ReportDetail", ReportDetailSchema)