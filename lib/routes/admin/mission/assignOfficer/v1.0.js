const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const MissionLogModel = require('../../../../models/missionLog')
const UserModel = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const logger = require('../../../../logger')

module.exports = (req, res) => {
  const {
   _id,
   user,
   unit
  } = req.body

  const validateParams = (next) => {
    if (!_id || !_id.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID nhiệm vụ là bắt buộc'
        }
      });
    }

    if (!user || !user.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID người dùng là bắt buộc'
        }
      });
    }

    if (!unit || !unit.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID đơn vị là bắt buộc'
        }
      });
    }

    next();
  }

  const checkMissionStatus = (next) => {
    MissionModel.findById(_id.trim())
      .then(mission => {
        if (!mission) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy nhiệm vụ'
            }
          });
        }

        if (mission.status !== 2) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Chỉ có thể thêm user khi nhiệm vụ ở trạng thái Đang Huy động lực lượng'
            }
          });
        }

        next(null, mission);
      })
      .catch(err => next(err));
  }

  const updateMission = (mission, next) => {
    // Kiểm tra user có thuộc unit không
    UserModel.findById(user.trim())
      .then(userDoc => {
        if (!userDoc) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy người dùng'
            }
          });
        }

        // Kiểm tra user có thuộc unit không
        const userUnits = userDoc.units.map(u => u.toString());
        if (!userUnits.includes(unit.trim())) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Người dùng không thuộc đơn vị này'
            }
          });
        }

        // Cập nhật users array (thêm user nếu chưa có)
        if (!mission.users.includes(user.trim())) {
          mission.users.push(user.trim());
        }

        // Tìm và cập nhật assignInfo cho unit tương ứng
        let unitAssignInfo = mission.assignInfo.find(info => info.unit.toString() === unit.trim());
        
        if (unitAssignInfo) {
          // Kiểm tra numberOfUsers phải > 0
          if (!unitAssignInfo.numberOfUsers || unitAssignInfo.numberOfUsers <= 0) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Thông báo',
                body: 'Đơn vị này không được phép có user (numberOfUsers = 0)'
              }
            });
          }

          // Kiểm tra số lượng user hiện tại có vượt quá numberOfUsers không
          if (unitAssignInfo.users.length >= unitAssignInfo.numberOfUsers) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Thông báo',
                body: `Đã đạt tối đa số lượng user cho đơn vị này (${unitAssignInfo.numberOfUsers})`
              }
            });
          }

          // Nếu đã có assignInfo cho unit này, thêm user vào nếu chưa có
          if (!unitAssignInfo.users.includes(user.trim())) {
            unitAssignInfo.users.push(user.trim());
          }
        } else {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy thông tin phân công cho đơn vị này'
            }
          });
        }

        // Cập nhật updatedAt
        mission.updatedAt = Date.now();

        mission.save()
          .then(() => {
            // Populate thông tin để trả về
            return MissionModel.findById(mission._id)
              .populate('createdBy', 'name')
              .populate('assignInfo.unit', 'name')
              .populate('assignInfo.users', 'name phones idNumber avatar')
              .populate('users', 'name phones idNumber avatar');
          })
          .then(updatedMission => {
            next(null, updatedMission);
          })
          .catch(err => next(err));
      })
      .catch(err => next(err));
  }

  const createMissionLog = (updatedMission, next) => {
    const logData = {
      user: req.user?._id || null,
      mission: updatedMission._id,
      message: `Thêm cán bộ cho nhiệm vụ`,
      action: 1, 
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const missionLog = new MissionLogModel(logData);
    missionLog.save()
      .then(() => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật nhiệm vụ thành công'
          },
          data: updatedMission
        });
      })
      .catch(err => {
        // Nếu ghi log thất bại, vẫn trả về thành công vì mission đã được cập nhật
        logger.logError([err], 'createMissionLog', logData);
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật nhiệm vụ thành công'
          },
          data: updatedMission
        });
      });
  }

  async.waterfall([
    validateParams,
    checkMissionStatus,
    updateMission,
    createMissionLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}