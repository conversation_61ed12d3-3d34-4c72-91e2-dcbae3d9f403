const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const MissionModel = require('../../../../models/mission');
const MissionLogModel = require('../../../../models/missionLog');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id } = req.body;

  // Validate required parameters
  if (!_id) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: MESSAGES.SYSTEM.WRONG_PARAMS,
    });
  }

  async.waterfall([
    // Check if mission exists
    (callback) => {
      MissionModel.findById(_id, (err, mission) => {
        if (err) return callback(err);
        if (!mission) {
          return callback({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.SYSTEM.WRONG_PARAMS,
          });
        }
        // Check if mission can be cancelled (not already completed or cancelled)
        if (mission.status === 3) { // COMPLETED
          return callback({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head:'Thông báo',
              body:'Nhiệm vụ đã hoàn thành, không thể hủy bỏ'
            }
          });
        }
        if (mission.status === 4) { // CANCELLED
          return callback({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head:'Thông báo',
              body:'Nhiệm vụ đã hủy bỏ, không thể hủy bỏ nữa'
            }
          });
        }
        callback(null, mission);
      });
    },
    // Update mission status to cancelled
    (mission, callback) => {
      MissionModel.findByIdAndUpdate(
        _id,
        {
          status: 4, // CANCELLED
          updatedAt: Date.now(),
        },
        { new: true },
        (err, updatedMission) => {
          if (err) return callback(err);
          callback(null, updatedMission);
        }
      );
    },
    // Create mission log entry
    (updatedMission, callback) => {
      const missionLog = new MissionLogModel({
        user: userId,
        mission: _id,
        message: `Nhiệm vụ đã được hủy bỏ`,
        action: 4
      });

      missionLog.save((err) => {
        if (err) return callback(err);
        callback(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head:'Thông báo',
            body:'Hủy nhiệm vụ thành công'
          },
          data: updatedMission,
        });
      });
    },
  ], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
