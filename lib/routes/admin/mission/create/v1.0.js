const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const MissionLogModel = require('../../../../models/missionLog')

const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')

module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    name,
    description,
    location,
    type,
    assignInfo,
    code
  } = req.body

  const validateParams = (next) => {
    // Kiểm tra các trường bắt buộc
    if (!name || !name.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tên nhiệm vụ không được để trống'
        }
      });
    }

    if (!location || !location.address) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Đ<PERSON>a điểm không được để trống'
        }
      });
    }

    if (!type || !['self_assign', 'unit_assign'].includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Loại nhiệm vụ không hợp lệ'
        }
      });
    }

    // Kiểm tra assignInfo cho từng loại
    if (type === 'self_assign') {
      if (!assignInfo || !Array.isArray(assignInfo) || assignInfo.length === 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thông tin phân công không được để trống'
          }
        });
      }

      // Kiểm tra mỗi unit phải có numberOfUsers
      for (let assign of assignInfo) {
        if (!assign.unit || !assign.numberOfUsers || assign.numberOfUsers <= 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Mỗi đơn vị phải có số lượng cán bộ cụ thể'
            }
          });
        }
      }
    } else if (type === 'unit_assign') {
      if (!assignInfo || !Array.isArray(assignInfo) || assignInfo.length === 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thông tin phân công không được để trống'
          }
        });
      }

      // Kiểm tra mỗi assign phải có unit
      for (let assign of assignInfo) {
        if (!assign.unit) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Mỗi phân công phải có đơn vị'
            }
          });
        }
      }
    }

    next();
  }

  const checkMissionExists = (next) => {
    if (!code) {
      return next();
    }

    MissionModel.findOne({ code: code.trim() })
      .then(existingMission => {
        if (existingMission) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Mã nhiệm vụ đã tồn tại'
            }
          });
        }
        next();
      })
      .catch(err => next(err));
  }

  const createMission = (next) => {
    // Xử lý assignInfo dựa trên type
    let processedAssignInfo = assignInfo;
    if (type === 'unit_assign') {
      // Xóa numberOfUsers khỏi assignInfo khi type là unit_assign
      processedAssignInfo = assignInfo.map(assign => {
        const { numberOfUsers, ...assignWithoutNumberOfUsers } = assign;
        return assignWithoutNumberOfUsers;
      });
    }

    const missionData = {
      name: name.trim(),
      description: description ? description.trim() : '',
      location: {
        address: location.address,
        coordinates: location.coordinates || []
      },
      type,
      assignInfo: processedAssignInfo,
      createdBy: userId,
      status: 1
    };

    // Nếu có code được gửi lên, sử dụng code đó
    if (code && code.trim()) {
      missionData.code = code.trim();
    }

    const mission = new MissionModel(missionData);
    
    mission.save()
      .then(savedMission => {
        next(null, savedMission);
      })
      .catch(err => next(err));
  }

  const logMission = (mission, next) => {
    const logData = {
      user: userId,
      mission: mission._id,
      message: `Tạo nhiệm vụ mới`,
      action: 0, // CREATE
    };

    const missionLog = new MissionLogModel(logData);
    
    missionLog.save()
      .then(() => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Tạo nhiệm vụ thành công'
          },
          data: mission
        });
      })
      .catch(err => next(err));
  }

  async.waterfall([
    validateParams,
    checkMissionExists,
    createMission,
    logMission
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}