const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const MissionLogModel = require('../../../../models/missionLog')
const UserModel = require('../../../../models/user')
const SavedNotificationModel = require('../../../../models/savedNotification')
const PushNotifyManager = require('../../../../jobs/pushNotify')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const logger = require('../../../../logger')
const MailUtil = require('../../../../util/mail')

module.exports = (req, res) => {
  const {
   _id
  } = req.body

  const validateParams = (next) => {
    if (!_id || !_id.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID nhiệm vụ là bắt buộc'
        }
      });
    }

    next();
  }

  const checkMissionStatus = (next) => {
    MissionModel.findById(_id.trim())
      .then(mission => {
        if (!mission) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy nhiệm vụ'
            }
          });
        }

        if (mission.status !== 1) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Chỉ có thể bắt đầu nhiệm vụ khi ở trạng thái Khởi tạo'
            }
          });
        }

        next(null, mission);
      })
      .catch(err => next(err));
  }

  const updateMissionStatus = (mission, next) => {
    // Cập nhật status từ 1 sang 2 (IN_PROGRESS)
    mission.status = 2;
    mission.updatedAt = Date.now();

    mission.save()
      .then(() => {
        // Populate thông tin để trả về
        return MissionModel.findById(mission._id)
          .populate('createdBy', 'name')
          .populate('assignInfo.unit', 'name')
          .populate('assignInfo.users', 'name phones idNumber avatar')
          .populate('users', 'name phones idNumber avatar');
      })
      .then(updatedMission => {
        next(null, updatedMission);
      })
      .catch(err => next(err));
  }

  const createMissionLog = (updatedMission, next) => {
    const logData = {
      user: req.user?._id || null,
      mission: updatedMission._id,
      message: `Bắt đầu nhiệm vụ`,
      action: 2, // Action cho việc bắt đầu nhiệm vụ
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const missionLog = new MissionLogModel(logData);
    missionLog.save()
      .then(() => {
        next(null, updatedMission);
      })
      .catch(err => {
        // Nếu ghi log thất bại, vẫn trả về thành công vì mission đã được cập nhật
        logger.logError([err], 'createMissionLog', logData);
        next(null, updatedMission);
      });
  }

  const notifyTeamLeaders = (updatedMission, next) => {
    // Chỉ gửi thông báo khi mission có type=unit_assign và status=2 (Đang Huy động lực lượng)
    if (updatedMission.type !== 'unit_assign' || updatedMission.status !== 2) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Bắt đầu nhiệm vụ thành công'
        },
        data: updatedMission
      });
    }

    // Lấy danh sách các unit từ assignInfo
    const unitIds = updatedMission.assignInfo.map(info => info.unit);

    if (unitIds.length === 0) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Bắt đầu nhiệm vụ thành công'
        },
        data: updatedMission
      });
    }

    // Tìm tất cả tổ trưởng của các tổ trong assignInfo
    UserModel.find({
      managedUnits: { $in: unitIds },
      status: 1
    })
    .populate('positions', 'name')
    .populate('managedUnits', 'name')
    .select('_id name managedUnits positions')
    .lean()
    .then(users => {
      // Lọc những user có position name=Tổ trưởng
      const teamLeaders = users.filter(user => 
        user.positions && user.positions.some(pos => pos.name === 'Tổ trưởng' || pos.name === 'Tổ Trưởng')
      );

      if (teamLeaders.length === 0) {
        return next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Bắt đầu nhiệm vụ thành công'
          },
          data: updatedMission
        });
      }

      // Chuẩn bị thông báo
      const title = 'Điều động lực lượng thực hiện nhiệm vụ';
      const description = `Nhiệm vụ "${updatedMission.name}" đã chuyển sang trạng thái huy động lực lượng. Vui lòng vào ứng dụng để điều động cán bộ trong tổ thực hiện nhiệm vụ.`;
      
      const notificationData = {
        link: 'MainContainer',
        extras: {
          missionId: updatedMission._id.toString(),
          tabIndex: 0
        },
        linkWeb: `/admin/mission/${updatedMission._id}`
      };

      // Tạo saved notification
      const savedNotificationData = {
        title: title,
        description: description,
        users: teamLeaders.map(leader => leader._id),
        type: 'user',
        data: notificationData,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      SavedNotificationModel.create(savedNotificationData)
        .then(savedNotification => {
          // Gửi push notification cho từng tổ trưởng
          const notificationPromises = teamLeaders.map(leader => {
            return PushNotifyManager.sendToMember(
              leader._id,
              title,
              description,
              notificationData,
              'mission_assignment',
              'ioc'
            ).catch(error => {
              console.error(`Failed to send notification to team leader ${leader._id}:`, error);
            });
          });

          Promise.allSettled(notificationPromises)
            .then(() => {
              console.log(`Sent mission assignment notifications to ${teamLeaders.length} team leaders`);
              next(null, {
                code: CONSTANTS.CODE.SUCCESS,
                message: {
                  head: 'Thông báo',
                  body: 'Bắt đầu nhiệm vụ thành công'
                },
                data: updatedMission
              });
            });
        })
        .catch(err => {
          console.error('Error creating saved notification:', err);
          // Vẫn trả về thành công vì mission đã được cập nhật
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Bắt đầu nhiệm vụ thành công'
            },
            data: updatedMission
          });
        });
    })
    .catch(err => {
      console.error('Error finding team leaders:', err);
      // Vẫn trả về thành công vì mission đã được cập nhật
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Bắt đầu nhiệm vụ thành công'
        },
        data: updatedMission
      });
    });
  }

  async.waterfall([
    validateParams,
    checkMissionStatus,
    updateMissionStatus,
    createMissionLog,
    notifyTeamLeaders
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}