const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const MissionLogModel = require('../../../../models/missionLog')

const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const logger = require('../../../../logger')
const MailUtil = require('../../../../util/mail')

module.exports = (req, res) => {
  const userId = req.user.id;
  const missionId = req.body._id;
  const {
    name,
    description,
    location,
    type,
    assignInfo,
    code
  } = req.body

  const validateParams = (next) => {
    // Kiểm tra missionId
    if (!missionId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID nhiệm vụ không được để trống'
        }
      });
    }

    // Kiểm tra các trường bắt buộc
    if (!name || !name.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tên nhiệm vụ không được để trống'
        }
      });
    }

    if (!location || !location.address) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Địa điểm không được để trống'
        }
      });
    }

    if (!type || !['self_assign', 'unit_assign'].includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Loại nhiệm vụ không hợp lệ'
        }
      });
    }

    // Kiểm tra assignInfo cho từng loại
    if (type === 'self_assign') {
      if (!assignInfo || !Array.isArray(assignInfo) || assignInfo.length === 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thông tin phân công không được để trống'
          }
        });
      }

      // Kiểm tra mỗi unit phải có numberOfUsers
      for (let assign of assignInfo) {
        if (!assign.unit || !assign.numberOfUsers || assign.numberOfUsers <= 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Mỗi đơn vị phải có số lượng cán bộ cụ thể'
            }
          });
        }
      }
    } else if (type === 'unit_assign') {
      if (!assignInfo || !Array.isArray(assignInfo) || assignInfo.length === 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thông tin phân công không được để trống'
          }
        });
      }

      // Kiểm tra mỗi assign phải có unit
      for (let assign of assignInfo) {
        if (!assign.unit) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Mỗi phân công phải có đơn vị'
            }
          });
        }
      }
    }

    next();
  }

  const findAndValidateMission = (next) => {
    MissionModel.findById(missionId)
      .then(mission => {
        if (!mission) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Nhiệm vụ không tồn tại'
            }
          });
        }

        if (mission.status !== 1) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Chỉ có thể cập nhật nhiệm vụ ở trạng thái Khởi tạo'
            }
          });
        }

        next(null, mission);
      })
      .catch(err => next(err));
  }

  const checkCodeUnique = (mission, next) => {
    // Chỉ kiểm tra unique code nếu code được thay đổi
    if (!code || !code.trim() || mission.code === code.trim()) {
      return next(null, mission);
    }

    MissionModel.findOne({ code: code.trim(), _id: { $ne: missionId } })
      .then(existingMission => {
        if (existingMission) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Mã nhiệm vụ đã tồn tại'
            }
          });
        }
        next(null, mission);
      })
      .catch(err => next(err));
  }
  const updateMission = (mission, next) => {
    // Xử lý assignInfo dựa trên type
    let processedAssignInfo = assignInfo;
    if (type === 'unit_assign') {
      // Xóa numberOfUsers khỏi assignInfo khi type là unit_assign
      processedAssignInfo = assignInfo.map(assign => {
        const { numberOfUsers, ...assignWithoutNumberOfUsers } = assign;
        return assignWithoutNumberOfUsers;
      });
    }

    // Cập nhật các trường dữ liệu
    mission.name = name.trim();
    mission.description = description ? description.trim() : '';
    mission.location = {
      address: location.address,
      coordinates: location.coordinates || []
    };
    mission.type = type;
    mission.assignInfo = processedAssignInfo;
    mission.updatedAt = Date.now();


    // Nếu có code được gửi lên, cập nhật code
    if (code && code.trim()) {
      mission.code = code.trim();
    }
    
    mission.save()
      .then(updatedMission => {
        next(null, updatedMission);
      })
      .catch(err => next(err));
  }

  const logMission = (mission, next) => {
    const logData = {
      user: userId,
      mission: mission._id,
      message: `Cập nhật nhiệm vụ`,
      action: 1, // UPDATE
    };

    const missionLog = new MissionLogModel(logData);
    
    missionLog.save()
      .then(() => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật nhiệm vụ thành công'
          },
          data: mission
        });
      })
      .catch(err => next(err));
  }

  async.waterfall([
    validateParams,
    findAndValidateMission,
    checkCodeUnique,
    updateMission,
    logMission
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}