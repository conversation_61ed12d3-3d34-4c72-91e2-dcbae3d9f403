const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const statisticsService = require('../../../services/statisticsService');
const statisticsMetadataService = require('../../../services/statisticsMetadataService');
const statisticsUtils = require('../../../utils/statisticsUtils');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API lấy 3 vụ việc mới nhất (Latest Incidents)
 * POST /api/v1.0/statistics/latest-incidents
 *
 * Trả về 3 vụ việc mới nhất từ trường metrics của các báo cáo có jobType với chartTypes chứa "highlight"
 * Logic: Lấy vụ việc từ metrics của báo cáo mới nhất trước, nếu không đủ 3 thì lấy thêm từ báo cáo tiếp theo
 * C<PERSON> thể filter theo khu vực thông qua query parameter
 * Dữ liệu này được sử dụng để hiển thị các vụ việc nổi bật mới nhất trên dashboard
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    area // ID khu vực để filter (optional)
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      area: Joi.objectId().optional() // Khu vực filter là optional
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  /**
   * Check cache trước khi xử lý
   */
  const getLatestIncidentsFromCache = (next) => {
    statisticsMetadataService.getCachedStatisticsData(
      'latest_incidents',
      area
    )
      .then((cachedData) => {
        if (cachedData) {
          console.log('[Cache Hit] Latest incidents from cache');
          result = {
            success: true,
            data: cachedData,
            fromCache: true,
            cacheTimestamp: Date.now()
          };
        }

        next();
      })
      .catch(error => {
        console.error('[Cache Error] Failed to get latest incidents from cache:', error);
        return next();
      });
  };

  /**
   * Lấy danh sách vụ việc mới nhất từ service với cache layer
   */
  const getLatestIncidents = (next) => {
    if (result) {
      return next();
    }

    try {
      // Cache miss - gọi service
      console.log('[Cache Miss] Fetching latest incidents from database');
      statisticsService.getLatestIncidents({
        area,
        userId
      })
        .then((serviceResult) => {
          if (!serviceResult || !serviceResult.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: serviceResult?.message || MESSAGES.SYSTEM.ERROR
            });
          }

          // Cache kết quả
          try {
            statisticsMetadataService.cacheStatisticsData(
              'latest_incidents',
              serviceResult.data,
              area
            );
            console.log('[Cache Set] Cached latest incidents data');
          } catch (cacheError) {
            console.error('[Cache Error] Failed to cache latest incidents:', cacheError);
          }

          result = {
            ...serviceResult,
            fromCache: false,
            cacheTimestamp: Date.now()
          };
          next();
        })
        .catch((error) => {
          console.error('[API Error] Error in getLatestIncidents:', error);
          return next(error);
        });
    } catch (error) {
      console.error('[API Error] Error in getLatestIncidents:', error);
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });

    // Ghi log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'get_latest_incidents',
        description: 'Xem 3 vụ việc mới nhất',
        data: req.body,
        updatedData: {
          totalIncidents: result.data?.total || 0,
          hasAreaFilter: !!area,
          areaId: area,
          fromCache: result.data?.fromCache || false
        }
      }, () => { });
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getLatestIncidentsFromCache,
    getLatestIncidents,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
