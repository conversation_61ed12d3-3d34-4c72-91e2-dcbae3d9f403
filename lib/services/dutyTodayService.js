const _ = require('lodash');

// Models
const DutyShift = require('../models/dutyShift');

// Utils
const statisticsUtils = require('../utils/statisticsUtils');

/**
 * Service lấy danh sách lịch trực của tất cả cán bộ trong ngày hiện tại
 * Dành cho chỉ huy xem tổng quan
 */
class DutyTodayService {

  /**
   * Lấy danh sách lịch trực hôm nay của tất cả cán bộ
   * @param {Object} params - Tham số filter, sort, pagination
   * @returns {Object} Kết quả với phân trang
   */
  async getAllDutyShiftsToday(params = {}) {
    try {
      const { filters = {}, sortBy = 'name', sortOrder = 'asc', page = 1, limit = 20, date } = params;

      // Tính toán khoảng thời gian hôm nay
      const todayRange = statisticsUtils.getTimeRange('custom', date, date);

      // L<PERSON>y tất cả ca trực hôm nay
      const dutyShifts = await this.fetchTodayDutyShifts(todayRange, filters);

      // Nhóm theo cán bộ
      const groupedByOfficer = this.groupDutyShiftsByOfficer(dutyShifts);

      // Áp dụng sắp xếp
      const sortedOfficers = await this.applySorting(groupedByOfficer, sortBy, sortOrder);

      // Áp dụng phân trang
      const paginatedResult = this.applyPagination(sortedOfficers, page, limit);

      // Tạo thống kê tổng quan
      const summary = this.generateSummary(dutyShifts);

      return {
        success: true,
        data: {
          pagination: paginatedResult.pagination,
          summary,
          dutySchedules: paginatedResult.data
        }
      };

    } catch (error) {
      console.error('Error in getAllDutyShiftsToday:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Lấy tất cả ca trực hôm nay từ database
   * Tái sử dụng logic query từ scheduleAggregationService.getDutyShifts()
   */
  async fetchTodayDutyShifts(todayRange, filters) {
    const query = {
      // Lấy ca trực có giao với ngày hôm nay (bao gồm ca qua đêm)
      $or: [
        // Ca bắt đầu hôm nay
        {
          startTime: { $gte: todayRange.startTimestamp, $lte: todayRange.endTimestamp }
        },
        // Ca kết thúc hôm nay
        {
          endTime: { $gt: todayRange.startTimestamp, $lte: todayRange.endTimestamp }
        },
        // Ca bao trùm cả ngày hôm nay
        {
          startTime: { $lte: todayRange.startTimestamp },
          endTime: { $gte: todayRange.endTimestamp }
        }
      ]
    };

    // Áp dụng filters
    this.applyFilters(query, filters);

    const shifts = await DutyShift.find(query)
      .populate({
        path: 'officer',
        select: 'name idNumber avatar positions units managedUnits',
        populate: [{
          path: 'units',
          select: 'name parentPath'
        }, {
          path: 'positions',
          select: 'name'
        }, {
          path: 'managedUnits',
          select: 'name parentPath'
        }]
      })
      .lean();

    return shifts;
  }

  /**
   * Áp dụng các bộ lọc vào query
   */
  applyFilters(query, filters) {
    // Lọc theo đơn vị
    if (filters.unit) {
      query.unit = filters.unit;
    }

    // Lọc theo cán bộ cụ thể
    if (filters.officer) {
      query.officer = filters.officer;
    }

    // Lọc theo loại lịch trực
    if (filters.dutyType) {
      const dutyTypeFilters = [];

      // Mapping các loại lịch trực
      const dutyTypeMapping = {
        'specialized': 'dutySpecializedSchedule',
        'criminal': 'dutyCriminalSchedule',
        'main': 'dutyMainSchedule',
        'sub': 'dutySubSchedule',
        'location': 'dutyLocationSchedule',
        'patrol': 'dutyPatrolSchedule',
        'stadium': 'dutyStadiumSchedule',
        'emergency': 'dutyEmergencySchedule'
      };

      if (dutyTypeMapping[filters.dutyType]) {
        const condition = {};
        condition[dutyTypeMapping[filters.dutyType]] = { $exists: true, $ne: null };
        dutyTypeFilters.push(condition);
      }

      if (dutyTypeFilters.length > 0) {
        query.$and = query.$and || [];
        query.$and.push({ $or: dutyTypeFilters });
      }
    }
  }

  /**
   * Nhóm ca trực theo cán bộ
   * Tái sử dụng logic groupBy từ lodash như trong scheduleAggregationService
   */
  groupDutyShiftsByOfficer(dutyShifts) {
    const grouped = _.groupBy(dutyShifts, shift => shift.officer._id.toString());

    return Object.keys(grouped).map(officerId => {
      const shifts = grouped[officerId];
      const officer = shifts[0].officer; // Lấy thông tin officer từ shift đầu tiên

      return {
        officer: officer,
        dutyShifts: shifts.map(shift => ({
          _id: shift._id,
          title: shift.name,
          startTime: shift.startTime,
          endTime: shift.endTime,
          location: shift.locationDuty,
          status: shift.status,
          forLeader: shift.forLeader,
          description: shift.description,
          notes: shift.notes,
          hasEquipment: shift.hasEquipment,
          type: this.getDutyType(shift),
          createdAt: shift.createdAt
        }))
      };
    });
  }

  /**
   * Xác định loại lịch trực từ shift
   */
  getDutyType(shift) {
    if (shift.dutySpecializedSchedule) return 'specialized';
    if (shift.dutyCriminalSchedule) return 'criminal';
    if (shift.dutyMainSchedule) return 'main';
    if (shift.dutySubSchedule) return 'sub';
    if (shift.dutyLocationSchedule) return 'location';
    if (shift.dutyPatrolSchedule) return 'patrol';
    if (shift.dutyStadiumSchedule) return 'stadium';
    if (shift.dutyEmergencySchedule) return 'emergency';
    return 'manual';
  }

  /**
   * Áp dụng sắp xếp
   * Tái sử dụng logic sort từ lodash
   */
  async applySorting(groupedOfficers, sortBy, sortOrder) {
    const sortDirection = sortOrder === 'desc' ? 'desc' : 'asc';

    return _.orderBy(groupedOfficers, [
      (item) => {
        switch (sortBy) {
          case 'name':
            // Sắp xếp theo tên: lấy từ cuối cùng (họ tên Việt Nam)
            const getLastName = (fullName) => {
              if (!fullName) return '';
              const nameParts = fullName.trim().split(' ');
              return nameParts[nameParts.length - 1];
            };

            return getLastName(item.officer.name);
          case 'unit':
            // Lấy tên đơn vị từ ca trực đầu tiên
            return item.officer?.units[item.officer?.units.length - 1]?.name || '';
          default:
            return item.officer.name;
        }
      }
    ], [sortDirection]);
  }

  /**
   * Áp dụng phân trang
   * Tái sử dụng logic pagination chuẩn
   */
  applyPagination(data, page, limit) {
    const totalRecords = data.length;
    const totalPages = Math.ceil(totalRecords / limit);
    const offset = (page - 1) * limit;
    const paginatedData = data.slice(offset, offset + limit);

    return {
      data: paginatedData,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalRecords,
        limit: parseInt(limit),
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Tạo thống kê tổng quan
   * Tái sử dụng logic từ scheduleAggregationService.generateSummary()
   */
  generateSummary(dutyShifts) {
    const uniqueOfficers = new Set();
    const dutyTypes = {};

    dutyShifts.forEach(shift => {
      // Đếm số cán bộ duy nhất
      uniqueOfficers.add(shift.officer._id.toString());

      // Đếm theo loại lịch trực
      const dutyType = this.getDutyType(shift);
      dutyTypes[dutyType] = (dutyTypes[dutyType] || 0) + 1;
    });

    return {
      totalOfficersOnDuty: uniqueOfficers.size,
      totalDutyShifts: dutyShifts.length,
      dutyTypes
    };
  }
}

module.exports = new DutyTodayService();