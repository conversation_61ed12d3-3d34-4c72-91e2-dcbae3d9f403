const _ = require('lodash');
const moment = require('moment');

// Models
const WorkSchedule = require('../models/workSchedule');
const MeetingSchedule = require('../models/meetingSchedule');
const DutyShift = require('../models/dutyShift');
const User = require('../models/user');

const statisticsUtils = require('../utils/statisticsUtils');

/**
 * Service tổng hợp lịch làm việc từ 3 nguồn: work, duty, meeting
 * Nhóm theo ngày và sắp xếp theo thời gian
 */
class ScheduleAggregationService {

  /**
   * Lấy lịch tổng hợp được nhóm theo ngày
   * @param {string} userId - ID của user
   * @param {Object} params - Tham số filter
   * @returns {Object} Kết quả tổng hợp
   */
  async getGroupedSchedule(userId, params = {}) {
    try {
      const { startDate, endDate, scheduleType } = params;

      // Tính toán khoảng thời gian
      const dateRange = statisticsUtils.getTimeRange('custom', startDate, endDate);

      // Lấy dữ liệu từ 3 nguồn song song
      const [workSchedules, dutyShifts, meetingSchedules] = await Promise.all([
        this.getWorkSchedules(userId, dateRange, scheduleType),
        this.getDutyShifts(userId, dateRange, scheduleType),
        this.getMeetingSchedules(userId, dateRange, scheduleType)
      ]);

      // Merge và chuẩn hóa dữ liệu
      const allSchedules = this.mergeSchedules(workSchedules, dutyShifts, meetingSchedules);

      // Nhóm theo ngày
      const groupedByDate = this.groupSchedulesByDate(allSchedules);

      // Sắp xếp trong từng ngày và filter theo khoảng thời gian yêu cầu
      const sortedSchedules = this.sortSchedulesInEachDay(groupedByDate, dateRange);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy lịch tổng hợp thành công'
        },
        data: {
          dateRange,
          totalSchedules: allSchedules.length,
          schedulesByDate: sortedSchedules,
          summary: this.generateSummary(allSchedules)
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy lịch làm việc
   * @param {string} userId - ID của user
   * @param {Object} dateRange - Khoảng thời gian
   * @param {string} scheduleType - Loại lịch filter
   * @returns {Array} Danh sách lịch làm việc
   */
  async getWorkSchedules(userId, dateRange, scheduleType) {
    if (scheduleType && scheduleType !== 'work') {
      return [];
    }

    const query = {
      user: userId,
      status: 1
    };

    // Filter theo khoảng ngày (format DD-MM-YYYY)
    const dates = this.generateDateArray(dateRange.startDate, dateRange.endDate);
    query.date = { $in: dates };

    const schedules = await WorkSchedule.find(query)
      .populate('user', 'name idNumber avatar')
      .populate('createdBy', 'name')
      .lean();

    return schedules.map(schedule => ({
      id: schedule._id,
      type: 'work',
      title: _.includes(schedule.shifts.map(shift => shift.status), 'excused')
        ? 'Nghỉ'
        : _.includes(schedule.shifts.map(shift => shift.status), 'business_trip')
          ? 'Công tác'
          : 'Làm việc tại trụ sở',
      date: schedule.date,
      shifts: schedule.shifts,
      user: schedule.user,
      createdBy: schedule.createdBy,
      createdAt: schedule.createdAt,
      // Tính toán thời gian để sắp xếp
      sortTime: this.calculateWorkScheduleSortTime(schedule)
    }));
  }

  /**
   * Lấy lịch trực
   * @param {string} userId - ID của user
   * @param {Object} dateRange - Khoảng thời gian
   * @param {string} scheduleType - Loại lịch filter
   * @returns {Array} Danh sách lịch trực
   */
  async getDutyShifts(userId, dateRange, scheduleType) {
    if (scheduleType && scheduleType !== 'duty') {
      return [];
    }

    const query = {
      officer: userId,
      status: { $ne: 2 }, // Không lấy ca đã hủy
      // Lấy ca trực có giao với khoảng thời gian (bao gồm ca qua đêm)
      $or: [
        // Ca bắt đầu trong khoảng thời gian
        {
          startTime: { $gte: dateRange.startTimestamp, $lte: dateRange.endTimestamp }
        },
        // Ca kết thúc trong khoảng thời gian
        {
          endTime: { $gte: dateRange.startTimestamp, $lte: dateRange.endTimestamp }
        },
        // Ca bao trùm toàn bộ khoảng thời gian
        {
          startTime: { $lte: dateRange.startTimestamp },
          endTime: { $gte: dateRange.endTimestamp }
        }
      ]
    };

    const shifts = await DutyShift.find(query)
      .populate('officer', 'name idNumber avatar')
      .populate('unit', 'name')
      .populate('assignedBy', 'name')
      .lean();

    const result = [];

    shifts.forEach(shift => {
      const startMoment = moment(shift.startTime);
      const endMoment = moment(shift.endTime);

      // Tạo bản ghi cho ngày bắt đầu
      const baseRecord = {
        id: shift._id,
        type: 'duty',
        title: shift.name || 'Trực',
        date: startMoment.format('DD-MM-YYYY'),
        startTime: shift.startTime,
        endTime: shift.endTime,
        location: shift.locationDuty,
        unit: shift.unit,
        officer: shift.officer,
        assignedBy: shift.assignedBy,
        status: shift.status,
        forLeader: shift.forLeader,
        description: shift.description,
        notes: shift.notes,
        hasEquipment: shift.hasEquipment,
        createdAt: shift.createdAt,
        // Thời gian để sắp xếp
        sortTime: shift.startTime
      };

      result.push(baseRecord);

      // Kiểm tra ca trực qua đêm (khác ngày và endTime không phải 0h00)
      if (endMoment.format('DD-MM-YYYY') !== startMoment.format('DD-MM-YYYY')) {

        // Kiểm tra endTime có phải 0h00 không (ca làm trọn ngày)
        const isFullDayShift = endMoment.hour() === 0 && endMoment.minute() === 0;

        // Chỉ tạo bản ghi thứ 2 nếu không phải ca làm trọn ngày
        if (!isFullDayShift) {
          // Tạo bản ghi cho ngày kết thúc với sortTime = 0 (lên đầu danh sách)
          const nextDayRecord = {
            ...baseRecord,
            date: endMoment.format('DD-MM-YYYY'),
            sortTime: 0 // Sắp xếp lên đầu ngày thứ 2
          };

          result.push(nextDayRecord);
        }
      }
    });

    return result;
  }

  /**
   * Lấy lịch họp
   * @param {string} userId - ID của user
   * @param {Object} dateRange - Khoảng thời gian
   * @param {string} scheduleType - Loại lịch filter
   * @returns {Array} Danh sách lịch họp
   */
  async getMeetingSchedules(userId, dateRange, scheduleType) {
    if (scheduleType && scheduleType !== 'meeting') {
      return [];
    }

    // Lấy các đơn vị (units) của userId
    const userUnits = await this.getUserUnits(userId);

    const query = {
      status: 1,
      startTime: {
        $gte: dateRange.startTimestamp,
        $lte: dateRange.endTimestamp
      },
      $or: [
        { officers: userId },
        { units: { $in: userUnits } }
      ]
    };

    const meetings = await MeetingSchedule.find(query)
      .populate('officers', 'name idNumber avatar')
      .populate('units', 'name')
      .populate('assignedBy', 'name')
      .lean();

    return meetings.map(meeting => ({
      id: meeting._id,
      type: 'meeting',
      title: meeting.topic || 'Cuộc họp',
      date: moment(meeting.startTime).format('DD-MM-YYYY'),
      startTime: meeting.startTime,
      endTime: meeting.endTime,
      topic: meeting.topic,
      address: meeting.address,
      content: meeting.content,
      officers: meeting.officers,
      units: meeting.units,
      assignedBy: meeting.assignedBy,
      attachments: meeting.attachments,
      createdAt: meeting.createdAt,
      // Thời gian để sắp xếp
      sortTime: meeting.startTime
    }));
  }

  /**
   * Merge dữ liệu từ 3 nguồn
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} dutyShifts - Lịch trực
   * @param {Array} meetingSchedules - Lịch họp
   * @returns {Array} Danh sách tổng hợp
   */
  mergeSchedules(workSchedules, dutyShifts, meetingSchedules) {
    return [...workSchedules, ...dutyShifts, ...meetingSchedules];
  }

  /**
   * Nhóm lịch theo ngày
   * @param {Array} schedules - Danh sách lịch
   * @returns {Object} Lịch được nhóm theo ngày
   */
  groupSchedulesByDate(schedules) {
    return _.groupBy(schedules, 'date');
  }

  /**
   * Sắp xếp lịch trong từng ngày theo thời gian và filter theo khoảng thời gian yêu cầu
   * @param {Object} groupedSchedules - Lịch đã nhóm theo ngày
   * @param {Object} dateRange - Khoảng thời gian yêu cầu
   * @returns {Array} Lịch đã sắp xếp
   */
  sortSchedulesInEachDay(groupedSchedules, dateRange) {
    const sortedDays = [];

    // Tạo danh sách ngày trong khoảng yêu cầu
    const requestedDates = this.generateDateArray(dateRange.startDate, dateRange.endDate);

    requestedDates.forEach(date => {
      const daySchedules = groupedSchedules[date] || [];

      // Sắp xếp lịch trong ngày theo thời gian
      const sortedSchedules = daySchedules.sort((a, b) => {
        // Lịch có thời gian xác định lên trước
        if (a.sortTime && !b.sortTime) return -1;
        if (!a.sortTime && b.sortTime) return 1;
        if (!a.sortTime && !b.sortTime) return 0;

        return a.sortTime - b.sortTime;
      });

      // Format thông tin ngày
      const momentDate = moment(date, 'DD-MM-YYYY');

      sortedDays.push({
        date: date,
        dayOfWeek: this.getDayOfWeekName(momentDate.day()),
        totalSchedules: sortedSchedules.length,
        schedules: sortedSchedules
      });
    });

    return sortedDays;
  }

  /**
   * Tạo mảng ngày từ startDate đến endDate (format DD-MM-YYYY)
   * @param {string} startDate - Ngày bắt đầu
   * @param {string} endDate - Ngày kết thúc
   * @returns {Array} Mảng các ngày
   */
  generateDateArray(startDate, endDate) {
    const dates = [];
    const start = moment(startDate, 'DD-MM-YYYY');
    const end = moment(endDate, 'DD-MM-YYYY');

    let current = start.clone();
    while (current.isSameOrBefore(end)) {
      dates.push(current.format('DD-MM-YYYY'));
      current.add(1, 'day');
    }

    return dates;
  }

  /**
   * Tính toán thời gian sắp xếp cho lịch làm việc
   * @param {Object} schedule - Lịch làm việc
   * @returns {number|null} Timestamp để sắp xếp
   */
  calculateWorkScheduleSortTime(schedule) {
    if (!schedule.shifts || schedule.shifts.length === 0) {
      return null;
    }

    // Lấy ca sớm nhất trong ngày
    const earliestShift = schedule.shifts.reduce((earliest, shift) => {
      const shiftTime = this.parseTimeString(shift.startTime);
      const earliestTime = earliest ? this.parseTimeString(earliest.startTime) : null;

      if (!earliestTime || shiftTime < earliestTime) {
        return shift;
      }
      return earliest;
    }, null);

    if (!earliestShift) {
      return null;
    }

    // Chuyển đổi thành timestamp trong ngày
    const scheduleDate = moment(schedule.date, 'DD-MM-YYYY');
    const [hours, minutes] = earliestShift.startTime.split(':').map(Number);

    return scheduleDate.clone().hour(hours).minute(minutes).valueOf();
  }

  /**
   * Parse time string thành số phút từ đầu ngày
   * @param {string} timeStr - Chuỗi thời gian (HH:mm)
   * @returns {number} Số phút từ đầu ngày
   */
  parseTimeString(timeStr) {
    if (!timeStr) return 0;

    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Lấy tên thứ trong tuần
   * @param {number} dayOfWeek - Số thứ (0 = Chủ nhật)
   * @returns {string} Tên thứ
   */
  getDayOfWeekName(dayOfWeek) {
    const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
    return days[dayOfWeek];
  }

  /**
   * Tạo thống kê tổng quan
   * @param {Array} schedules - Danh sách tất cả lịch
   * @returns {Object} Thống kê
   */
  generateSummary(schedules) {
    const summary = {
      total: schedules.length,
      byType: {
        work: 0,
        duty: 0,
        meeting: 0
      },
      byDate: {}
    };

    schedules.forEach(schedule => {
      // Đếm theo loại
      summary.byType[schedule.type]++;

      // Đếm theo ngày
      if (!summary.byDate[schedule.date]) {
        summary.byDate[schedule.date] = 0;
      }
      summary.byDate[schedule.date]++;
    });

    return summary;
  }

  /**
   * Lấy danh sách đơn vị của user
   * @param {string} userId - ID của user
   * @returns {Promise<Array>} Danh sách đơn vị
   */
  getUserUnits(userId) {
    return User
      .findOne({ _id: userId })
      .select('units')
      .lean()
      .then(user => user.units || []);
  }
}

module.exports = new ScheduleAggregationService();
